# 微信小程序存储超限问题修复方案

## 问题描述

项目中出现了微信小程序本地存储超出大小限制的错误：
```
Error: APP-SERVICE-SDK:setStorageSync:fail:entry size limit reached
```

## 问题根因

1. **大数据对象存储**：用户信息中的`recommendedMajors`字段包含完整的专业树结构数据，数据量巨大
2. **重复存储**：在多个地方存储了包含大数据的用户信息
3. **存储策略不当**：将应该通过API获取的数据存储在本地

## 解决方案

### 1. 移除大数据对象存储

**修改文件：**
- `store/index.js`
- `pages/login/login.vue`

**修改内容：**
- 在用户信息对象中移除`recommendedMajors`字段
- 添加注释说明不再存储大数据对象

### 2. 改为API按需获取

**修改文件：**
- `pages/plan/questionnaire.vue`

**修改内容：**
- 不再从本地存储的用户信息中获取推荐专业数据
- 改为通过`getSubject()` API方法获取专业数据
- 避免在本地存储大量专业数据

### 3. 创建存储清理工具

**新增文件：**
- `utils/storage-cleaner.js`

**功能：**
- 自动检测和清理本地存储中的大数据对象
- 精简用户信息，只保留必要字段
- 清理Vuex store中的大数据
- 提供存储使用情况分析

### 4. 应用启动时自动清理

**修改文件：**
- `main.js`

**修改内容：**
- 导入存储清理工具
- 应用启动时自动检测并清理大数据
- 全局挂载清理工具供调试使用

### 5. 调试工具页面

**新增文件：**
- `pages/debug/storage-debug.vue`
- 在`pages.json`中注册页面

**功能：**
- 实时查看存储使用情况
- 手动触发存储清理
- 测试大数据存储限制
- 查看操作日志

## 技术细节

### 存储清理策略

1. **用户信息精简**：
   ```javascript
   const cleanedInfo = {
     score: myInfo.score,
     province: myInfo.province,
     grade: myInfo.grade || myInfo.nianji,
     secondSubject: myInfo.secondSubject,
     subjects: myInfo.subjects,
     ranking: myInfo.ranking,
     rankingRange: myInfo.rankingRange
     // 移除 recommendedMajors 字段
   };
   ```

2. **自动检测机制**：
   - 检查是否存在`recommendedMajors`字段
   - 计算数据大小，超过阈值时触发清理

3. **安全清理**：
   - 保留用户基本信息
   - 只清理大数据对象
   - 提供错误处理和日志记录

### API获取策略

- 专业数据改为通过`getmajordata`或`getmajordata1` API获取
- 按需加载，不在本地存储
- 减少内存占用和存储压力

## 使用方法

### 自动清理
应用启动时会自动检测并清理大数据，无需手动操作。

### 手动调试
访问调试页面：`/pages/debug/storage-debug`
- 查看当前存储使用情况
- 手动触发清理操作
- 测试存储限制

### 开发者工具
```javascript
// 在任何页面中使用
this.$storageCleaner.cleanAllLargeData();
this.$storageCleaner.getStorageInfo();
```

## 预期效果

1. **解决存储超限**：移除大数据对象，避免存储限制错误
2. **提升性能**：减少本地存储读写，提升应用启动速度
3. **降低内存占用**：按需获取数据，减少内存使用
4. **便于维护**：清晰的数据获取策略，便于后续维护

## 注意事项

1. **网络依赖**：专业数据改为API获取，需要网络连接
2. **缓存策略**：可考虑添加适当的内存缓存，避免重复请求
3. **兼容性**：保持与现有功能的兼容性
4. **监控**：建议添加存储使用监控，及时发现问题

## 测试建议

1. **功能测试**：确保专业选择功能正常工作
2. **性能测试**：测试应用启动速度和内存使用
3. **存储测试**：使用调试工具测试存储限制
4. **网络测试**：测试网络异常情况下的处理

## 后续优化

1. **缓存优化**：添加合理的内存缓存机制
2. **压缩存储**：对必要的存储数据进行压缩
3. **分页加载**：对大量数据实现分页加载
4. **离线支持**：考虑关键数据的离线缓存策略
