import createPersistedState from 'vuex-persistedstate'
import base from '@/network/baseUrl.js'
import apis from "network/apis.js"
import Vue from 'vue';
import dayjs from 'dayjs';
import Vuex from 'vuex';
Vue.use(Vuex)
const store = new Vuex.Store({
	state: {
		useInfo: uni.getStorageSync('sysInfo').useInfo || [],
	},
	getters: {
		getuseInfo(state) {
			return state.useInfo
		}
	},
	mutations: {
		setuseInfo(state, item) {
			state.useInfo = item
		},
	},
	actions: {
		gologinPage(){
			return new Promise((resolve) => {
				uni.login().then(res2 => {
					apis.wxLogin({
						code: res2.code
					}).then((res) => {
						console.log(res)
						if (res.code == 0) {
							let arr = []
							if (res.data.userRespVO.secondSubject) {
								arr = (res.data.userRespVO.secondSubject).split(',')
							}
							let myInfo = {
								score: res.data.userRespVO.score,
								province: res.data.userRespVO.province,
								nianji: res.data.userRespVO.grade,
								subjects: arr,
								ranking: res.data.userRespVO.ranking,
								rankingRange: res.data.userRespVO.rankingRange
								// 不再存储 recommendedMajors 大数据对象
							}
							let askInfo = res.data.userAssetsDO

							if (res.data.userRespVO.score) {
								uni.setStorageSync('myInfo', myInfo)
							}
							let isVip = false
							if (res.data.userAssetsDO.contentStartTime && res.data.userAssetsDO
								.contentEndTime) {

								let date1 = (dayjs(res.data.userAssetsDO.contentEndTime).format(
									'YYYY/MM/DD HH:mm:ss')); //开始时间

								let date2 = dayjs(res.data.userAssetsDO.contentStartTime).format(
									'YYYY/MM/DD HH:mm:ss'); //结束时间

								let diffTime = (new Date(date1)).getTime() - (new Date(
									date2)).getTime(); //时间差的毫秒数
								if (diffTime > 0) {
									isVip = true
								} else {
									isVip = false
								}

							} else {
								isVip = false
							}
							uni.setStorageSync('isVip', isVip)
							uni.setStorageSync('askInfo', askInfo)
							uni.setStorageSync('tys', askInfo.contentLeftCount)
							uni.setStorageSync('tws', askInfo.askLeftCount)
							uni.setStorageSync('token', res.data.accessToken)
							uni.setStorageSync('userId', res.data.userId)
							uni.setStorageSync('openId', res.data.userRespVO.openId)
							uni.setStorageSync('userName', res.data.userRespVO.username?res.data.userRespVO.username:res.data.userRespVO.nickname)
							uni.setStorageSync('nickname', res.data.userRespVO.nickname)
							resolve(res.data)
						} else {
							// 登录失败，返回null
							resolve(null)
						}
					}).catch((err) => {
						console.error('登录失败:', err)
						// 发生错误，返回null
						resolve(null)
					})
				})
			})

		},
		getuseInfo(context, text) {
			return new Promise((resolve) => {
				apis.qygetByUserId({
					userId: uni.getStorageSync('userId')
				}).then((res) => {
					let askInfo = res.data
					let isVip = false
					if (askInfo && askInfo.contentStartTime && askInfo.contentEndTime) {

						let date1 = (dayjs(askInfo.contentEndTime).format(
							'YYYY/MM/DD HH:mm:ss'));

						let date2 = dayjs(askInfo.contentStartTime).format(
							'YYYY/MM/DD HH:mm:ss');

						let diffTime = (new Date(date1)).getTime() - (new Date(
							date2)).getTime();
						if (diffTime > 0) {
							isVip = true
						} else {
							isVip = false
						}

					} else {
						isVip = false
					}
					let currentTime = dayjs().format('YYYY/MM/DD HH:mm:ss')
					let tomorrowMidnight = dayjs().add(1, 'day').startOf('day');

					let formattedTime = tomorrowMidnight.format('YYYY/MM/DD HH:mm:ss');
					// console.log(currentTime)
					// console.log(formattedTime)
					let diffTimes = (new Date(formattedTime)).getTime() - (new Date(currentTime)).getTime();
					if(diffTimes<=0){
						askInfo.contentLeftCount = 3
						askInfo.askLeftCount = 10
					}
				    // console.log(diffTimes)
					uni.setStorageSync('isVip', isVip)
					uni.setStorageSync('askInfo', askInfo)
					uni.setStorageSync('tys', askInfo.contentLeftCount)
					uni.setStorageSync('tws', askInfo.askLeftCount)
					context.commit('setuseInfo', res.data)
					resolve(res.data)
				})
			})
		},

	},
	plugins: [
		createPersistedState({
			key: 'sysInfo',
			storage: {
				getItem: (key) => uni.getStorageSync(key), // 获取
				setItem: (key, value) => uni.setStorageSync(key, value), // 存储
				removeItem: (key) => uni.removeStorageSync(key) // 删除
			},
			reducer(val) {
				return {
					useInfo: val.useInfo
				}
			}
		})
	]
})

export default store