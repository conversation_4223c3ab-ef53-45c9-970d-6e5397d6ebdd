/**
 * 存储清理工具
 * 用于清理微信小程序本地存储中的大数据对象，解决存储超限问题
 */

class StorageCleaner {

  /**
   * 清理用户信息中的大数据对象
   */
  cleanUserInfo() {
    try {
      // 获取当前存储的用户信息
      const myInfo = uni.getStorageSync('myInfo');

      if (myInfo && typeof myInfo === 'object') {
        // 创建精简的用户信息对象，移除大数据字段
        const cleanedInfo = {
          score: myInfo.score,
          province: myInfo.province,
          grade: myInfo.grade || myInfo.nianji, // 兼容旧字段名
          secondSubject: myInfo.secondSubject,
          subjects: myInfo.subjects,
          ranking: myInfo.ranking,
          rankingRange: myInfo.rankingRange
          // 移除 recommendedMajors 字段
        };

        // 重新存储精简后的用户信息
        uni.setStorageSync('myInfo', cleanedInfo);
        console.log('用户信息已清理，移除了大数据对象');

        return true;
      }
    } catch (error) {
      console.error('清理用户信息失败:', error);
      return false;
    }
  }

  /**
   * 清理Vuex store中的大数据
   */
  cleanVuexStore() {
    try {
      // 获取sysInfo存储
      const sysInfo = uni.getStorageSync('sysInfo');

      if (sysInfo && typeof sysInfo === 'object' && sysInfo.useInfo) {
        // 如果useInfo中包含recommendedMajors，移除它
        if (sysInfo.useInfo.recommendedMajors) {
          delete sysInfo.useInfo.recommendedMajors;
          uni.setStorageSync('sysInfo', sysInfo);
          console.log('Vuex store中的大数据已清理');
        }

        return true;
      }
    } catch (error) {
      console.error('清理Vuex store失败:', error);
      return false;
    }
  }

  /**
   * 清理所有可能的大数据存储
   */
  cleanAllLargeData() {
    console.log('开始清理本地存储中的大数据对象...');

    const results = {
      userInfo: this.cleanUserInfo(),
      vuexStore: this.cleanVuexStore(),
      otherCleanup: this.cleanOtherLargeData()
    };

    // 强制垃圾回收（如果支持）
    if (typeof wx !== 'undefined' && wx.triggerGC) {
      wx.triggerGC();
    }

    console.log('存储清理完成:', results);
    return results;
  }

  /**
   * 清理其他可能的大数据存储
   */
  cleanOtherLargeData() {
    try {
      // 清理可能存储的专业数据缓存和编辑页面的大数据
      const keysToCheck = [
        'majorData',
        'recommendedMajorsCache',
        'subjectData',
        'majorTreeData',
        'editProfileOriginalData' // 清理编辑页面可能存储的大数据
      ];

      keysToCheck.forEach(key => {
        try {
          const data = uni.getStorageSync(key);
          if (data) {
            // 对于editProfileOriginalData，检查是否包含大数据字段
            if (key === 'editProfileOriginalData' && data.recommendedMajors) {
              // 创建精简版本，移除大数据字段
              const cleanData = {
                username: data.username,
                province: data.province,
                grade: data.grade,
                secondSubject: data.secondSubject,
                subjects: data.subjects,
                score: data.score,
                ranking: data.ranking,
                rankingRange: data.rankingRange
              };
              uni.setStorageSync(key, cleanData);
              console.log(`已清理存储键 ${key} 中的大数据字段`);
            } else if (key !== 'editProfileOriginalData') {
              // 其他键直接删除
              uni.removeStorageSync(key);
              console.log(`已清理存储键: ${key}`);
            }
          }
        } catch (error) {
          console.warn(`清理存储键 ${key} 失败:`, error);
        }
      });

      return true;
    } catch (error) {
      console.error('清理其他大数据失败:', error);
      return false;
    }
  }

  /**
   * 获取当前存储使用情况（简化版）
   */
  getStorageInfo() {
    try {
      const info = uni.getStorageInfoSync();
      console.log('当前存储信息:', info);
      return info;
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return null;
    }
  }

  /**
   * 检查是否需要清理
   */
  needsCleaning() {
    try {
      const myInfo = uni.getStorageSync('myInfo');

      // 检查是否包含大数据字段
      if (myInfo && myInfo.recommendedMajors) {
        const size = JSON.stringify(myInfo.recommendedMajors).length;
        console.log('发现大数据对象 recommendedMajors，大小:', (size / 1024).toFixed(2), 'KB');
        return true;
      }

      return false;
    } catch (error) {
      console.error('检查清理需求失败:', error);
      return true; // 出错时建议清理
    }
  }
}

// 创建单例实例
const storageCleaner = new StorageCleaner();

export default storageCleaner;
