import App from './App'
import tabBar from './components/tabBar.vue'
import base from '@/network/baseUrl.js'
import apis from "@/network/apis.js"
import store from '@/store/index.js'
import uView from '@/uni_modules/uview-ui'
import { preventReClick } from './utils/directive.js'
import common from './utils/common.js'
import uviewConfig from './utils/uview-config.js'
import { share, shareMixin } from './utils/share.js'
import storageCleaner from './utils/storage-cleaner.js'

// #ifndef VUE3
import Vue from 'vue'
Vue.use(uView)
// 覆盖uView默认配置
uni.$u.setConfig({
  // 设置全局props
  props: { ...uviewConfig }
})
Vue.use(preventReClick) // 注册防止重复点击指令

Vue.prototype.$apis = apis;
Vue.prototype.$base = base;
Vue.prototype.$store = store;
Vue.prototype.$common = common; // 全局挂载公共方法
Vue.prototype.$share = share; // 全局挂载分享方法

// 注册全局自定义指令
Vue.directive('prevent-click', {
  inserted(el, binding) {
    common.preventButtonReClick(el, binding.value || 2000);
  }
});

// 注册全局分享混入
Vue.mixin(shareMixin);

Vue.component('tab-bar', tabBar)
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'

// 应用启动时清理存储中的大数据对象
try {
  if (storageCleaner.needsCleaning()) {
    console.log('检测到需要清理存储，开始清理...');
    storageCleaner.cleanAllLargeData();
  }
} catch (error) {
  console.error('存储清理过程中出错:', error);
}

const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif