<template>
  <view class="container">
    <view class="header">
      <text class="title">存储调试工具</text>
    </view>
    
    <view class="section">
      <text class="section-title">存储信息</text>
      <view class="info-item" v-for="(value, key) in storageInfo" :key="key">
        <text class="info-label">{{key}}:</text>
        <text class="info-value">{{value}}</text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">存储详情</text>
      <view class="storage-item" v-for="(item, key) in storageDetails" :key="key">
        <view class="storage-header">
          <text class="storage-key">{{key}}</text>
          <text class="storage-size" v-if="item.sizeKB">{{item.sizeKB}} KB</text>
          <text class="storage-error" v-if="item.error">错误: {{item.error}}</text>
        </view>
        <view class="storage-content" v-if="!item.error">
          <text class="storage-preview">{{getPreview(key)}}</text>
        </view>
      </view>
    </view>
    
    <view class="actions">
      <button class="action-btn" @tap="refreshInfo">刷新信息</button>
      <button class="action-btn danger" @tap="cleanStorage">清理存储</button>
      <button class="action-btn" @tap="testLargeData">测试大数据</button>
    </view>
    
    <view class="logs">
      <text class="section-title">操作日志</text>
      <view class="log-item" v-for="(log, index) in logs" :key="index">
        <text class="log-time">{{log.time}}</text>
        <text class="log-message">{{log.message}}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      storageInfo: {},
      storageDetails: {},
      logs: []
    }
  },
  
  onLoad() {
    this.refreshInfo();
  },
  
  methods: {
    // 刷新存储信息
    refreshInfo() {
      try {
        const result = this.$storageCleaner.getStorageInfo();
        if (result) {
          this.storageInfo = result.info;
          this.storageDetails = result.details;
        }
        this.addLog('存储信息已刷新');
      } catch (error) {
        this.addLog('刷新存储信息失败: ' + error.message);
      }
    },
    
    // 清理存储
    cleanStorage() {
      try {
        const result = this.$storageCleaner.cleanAllLargeData();
        this.addLog('存储清理完成: ' + JSON.stringify(result));
        this.refreshInfo();
      } catch (error) {
        this.addLog('存储清理失败: ' + error.message);
      }
    },
    
    // 测试大数据存储
    testLargeData() {
      try {
        // 创建一个大数据对象用于测试
        const largeData = {
          categories: []
        };
        
        // 生成大量测试数据
        for (let i = 0; i < 100; i++) {
          const category = {
            id: i,
            name: `测试分类${i}`,
            children: []
          };
          
          for (let j = 0; j < 50; j++) {
            const subCategory = {
              id: `${i}_${j}`,
              name: `测试子分类${i}_${j}`,
              children: []
            };
            
            for (let k = 0; k < 20; k++) {
              subCategory.children.push({
                id: `${i}_${j}_${k}`,
                name: `测试专业${i}_${j}_${k}`,
                description: '这是一个测试专业的详细描述，包含很多文字内容用于测试存储大小限制。'.repeat(10),
                educationLevel: '本科（普通教育）',
                careerDirection: '测试就业方向',
                salary: '￥10.3万',
                graduateScale: '1000-2000人',
                maleFemaleRatio: '45:55'
              });
            }
            
            category.children.push(subCategory);
          }
          
          largeData.categories.push(category);
        }
        
        // 尝试存储大数据
        uni.setStorageSync('testLargeData', largeData);
        this.addLog('大数据测试存储成功');
        this.refreshInfo();
      } catch (error) {
        this.addLog('大数据测试存储失败: ' + error.message);
      }
    },
    
    // 获取存储内容预览
    getPreview(key) {
      try {
        const data = uni.getStorageSync(key);
        if (typeof data === 'object') {
          return JSON.stringify(data).substring(0, 100) + '...';
        }
        return String(data).substring(0, 100);
      } catch (error) {
        return '无法预览';
      }
    },
    
    // 添加日志
    addLog(message) {
      const now = new Date();
      const time = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;
      this.logs.unshift({
        time,
        message
      });
      
      // 只保留最近20条日志
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20);
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 30rpx 0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background: #fff;
  margin: 20rpx 0;
  padding: 30rpx;
  border-radius: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-label {
  font-weight: bold;
  color: #666;
}

.info-value {
  color: #333;
}

.storage-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.storage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.storage-key {
  font-weight: bold;
  color: #333;
}

.storage-size {
  color: #666;
  font-size: 24rpx;
}

.storage-error {
  color: #ff4444;
  font-size: 24rpx;
}

.storage-content {
  margin-top: 10rpx;
}

.storage-preview {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin: 30rpx 0;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.action-btn.danger {
  background: #ff4444;
}

.logs {
  background: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-top: 20rpx;
}

.log-item {
  display: flex;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.log-time {
  color: #666;
  font-size: 24rpx;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.log-message {
  color: #333;
  font-size: 26rpx;
  flex: 1;
}
</style>
