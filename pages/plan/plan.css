/* 规划报告页面样式 */
page {
  background: #fff5f0;
}

/* 页面整体容器 */
.page-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(to bottom, #f8f4ff, #fff5f0 60%);
}

/* 头部样式 */
.head {
  position: relative;
  z-index: 10;
}

.header1 {
  display: flex;
  flex-direction: row;
  position: relative;
  justify-content: center;
  align-items: center;
  line-height: 88rpx;
}

.header1 .text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  letter-spacing: 2rpx;
}

/* 列表样式 */
.list {
  padding: 20rpx 16rpx;
  padding-top: 12rpx;
  padding-bottom: 120rpx; /* 增加底部填充，避免内容被底部导航栏遮挡 */
  min-height: 80vh; /* 确保列表区域有足够的高度 */
}

.item {
  position: relative;
  margin-bottom: 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 133, 16, 0.08), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border: 1rpx solid rgba(255, 150, 66, 0.12);
}

.item:active {
  transform: translateY(1rpx) scale(0.99);
  box-shadow: 0 6rpx 18rpx rgba(255, 133, 16, 0.12), 0 1rpx 6rpx rgba(0, 0, 0, 0.06);
}

.boxs {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.box1 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 24rpx;
}

.box1 .left {
  font-size: 32rpx;
  opacity: 0;
  font-weight: 700;
}

.box1 .right {
  color: #976c55;
  font-weight: 700;
  font-size: 26rpx;
  margin-right: 120rpx;
  padding: 4rpx 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(151, 108, 85, 0.2);
}

.box2 {
  font-size: 28rpx;
  margin: 0 24rpx 24rpx 24rpx;
  background: linear-gradient(135deg, rgba(255, 240, 229, 0.95) 0%, rgba(255, 235, 215, 0.9) 100%);
  border-radius: 16rpx;
  color: #976C55;
  min-height: 80rpx;
  padding: 18rpx 24rpx;
  line-height: 42rpx;
  text-align: justify;
  letter-spacing: 0.5rpx;
  box-shadow: 0 3rpx 8rpx rgba(151, 108, 85, 0.08);
  border: 1rpx solid rgba(151, 108, 85, 0.1);
}

/* 体验版卡片样式 */
.item:nth-of-type(1) {
  transition: all 0.3s;
}

.item:nth-of-type(1)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #4CAF50 0%, #45A049 100%);
}

/* 专业版卡片样式 */
.item:nth-of-type(2) {
  box-shadow: 0 10rpx 28rpx rgba(252, 170, 60, 0.18), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(252, 170, 60, 0.2);
}

.item:nth-of-type(2)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #FF9642 0%, #FB6E3F 100%);
}

.item:nth-of-type(2) .box1 .right {
  color: #C74E11;
  font-weight: 800;
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(199, 78, 17, 0.3);
}

.item:nth-of-type(2) .box2 {
  background: linear-gradient(135deg, rgba(252, 170, 60, 0.95) 0%, rgba(251, 110, 63, 0.9) 100%);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(252, 170, 60, 0.25);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(252, 170, 60, 0.3);
}

/* 弹窗样式 */
.picbox {
  background-repeat: no-repeat;
  background-size: contain;
  width: 600rpx;
  position: relative;
}

.closeicon {
  position: absolute;
  right: -25rpx;
  top: 5rpx;
  z-index: 10;
}

.closeicon image {
  width: 64rpx;
  height: 64rpx;
  transition: all 0.3s;
}

.closeicon image:active {
  transform: scale(0.9);
}

.myboxs {
  margin: 100rpx 30rpx 20rpx 30rpx;
  height: 400rpx;
  flex-direction: column;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.myboxs .cont {
  margin-bottom: 40rpx;
}

.myboxs .cont text {
  font-weight: 700;
  letter-spacing: 2rpx;
  font-size: 32rpx;
  color: #AA7248;
  line-height: 54rpx;
}

.myboxs .cont text .bold {
  color: #e6702b;
  margin: 0 4rpx;
}

.myboxs .btn button {
  color: #fff;
  padding: 5rpx 80rpx;
  border-radius: 70rpx;
  font-size: 32rpx;
  background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);
  box-shadow: 0 6rpx 12rpx rgba(253, 88, 25, 0.2);
  transition: all 0.3s;
}

.myboxs .btn button:active {
  transform: scale(0.95);
}

.myboxs .btn button::after {
  border: none;
}

/* 加载动画样式 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8rpx);
}

.loading-spinner {
  width: 70rpx;
  height: 70rpx;
  animation: countloading 1s ease infinite;
  background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: contain;
  padding: 24rpx;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

@keyframes countloading {
  0% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

/* 无数据样式 */
.none {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 254, 254, 0.9) 100%);
  border-radius: 20rpx;
  height: calc(100vh - 360rpx);
  box-shadow: 0 8rpx 24rpx rgba(255, 133, 16, 0.06), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 150, 66, 0.08);
  position: relative;
  overflow: hidden;
}

.none::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 133, 16, 0.02) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

.none .icon image {
  width: 280rpx;
  height: 280rpx;
  opacity: 0.9;
}

.none .text {
  margin-top: 24rpx;
  color: #888;
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-8px) rotate(180deg);
  }
}

/* 背景样式 */
.bgs {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60vh;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
}

.bgs image {
  opacity: 0.7;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
}

/* 滚动容器样式 */
.scroll-container {
  height: calc(100vh - 180rpx);
  background: transparent;
  position: relative;
  z-index: 1;
  padding-bottom: 100rpx; /* 添加底部填充，确保内容不被底部导航栏遮挡 */
}

/* 下拉刷新样式 */
.refresh-container {
  width: 100%;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.refresh-icon {
  width: 40rpx;
  height: 40rpx;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 底部导航栏样式覆盖 */
.tabbar-container {
  background: #fff5f0 !important;
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(239, 239, 239, 0.5) !important;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.02);
}
